"""
测试搜索功能的逻辑正确性
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, MagicMock
import subprocess
import re
from typing import List, Tuple
from dataclasses import dataclass

# 直接复制必要的类定义来避免依赖问题
@dataclass
class CodeSnippet:
    """代码片段数据结构"""
    file_path: str
    line_number: int
    content: str
    context_before: str = ""
    context_after: str = ""

    def get_full_content(self) -> str:
        """获取包含上下文的完整内容"""
        full_content = []
        if self.context_before:
            full_content.append(self.context_before)
        full_content.append(self.content)
        if self.context_after:
            full_content.append(self.context_after)
        return "\n".join(full_content)

# 简化的文件过滤配置
FILE_FILTER_CONFIG = {
    "exclude_extensions": [".pyc", ".pyo", ".pyd", "__pycache__", ".git", ".DS_Store"],
    "include_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs", ".php", ".rb", ".md"],
    "max_file_size": 1024 * 1024  # 1MB 最大文件大小
}

class SearchEngine:
    """搜索引擎基类"""

    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.file_filter = FILE_FILTER_CONFIG

    def _is_valid_file(self, file_path: str) -> bool:
        """检查文件是否符合过滤条件"""
        # 检查文件扩展名
        if not any(file_path.endswith(ext) for ext in self.file_filter["include_extensions"]):
            return False

        # 检查排除的扩展名和目录
        if any(exclude in file_path for exclude in self.file_filter["exclude_extensions"]):
            return False

        # 检查文件大小
        try:
            if os.path.getsize(file_path) > self.file_filter["max_file_size"]:
                return False
        except OSError:
            return False

        return True

class GrepSearchEngine(SearchEngine):
    """基于grep的搜索引擎"""

    def search(self, query: str) -> List[CodeSnippet]:
        """使用grep搜索代码片段"""
        snippets = []

        try:
            # 构建grep命令
            cmd = [
                "grep",
                "-r",  # 递归搜索
                "-n",  # 显示行号
                "-i",  # 忽略大小写
                "-C", "3",  # 显示上下文3行
                query,
                self.repo_path
            ]

            # 添加文件类型过滤
            for ext in self.file_filter["include_extensions"]:
                cmd.extend(["--include", f"*{ext}"])

            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                snippets = self._parse_grep_output(result.stdout)

        except Exception as e:
            print(f"Grep搜索失败: {e}")

        return snippets

    def _parse_grep_output(self, output: str) -> List[CodeSnippet]:
        """解析grep输出结果"""
        snippets = []
        current_file = None
        current_lines = []

        for line in output.split('\n'):
            if not line.strip():
                if current_file and current_lines:
                    snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                    current_lines = []
                current_file = None
                continue

            # 解析grep输出格式：文件名:行号:内容 或 文件名-行号-内容（上下文）
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)

            if match:
                file_path, line_number, content = match.groups()
                if self._is_valid_file(file_path):
                    current_file = file_path
                    current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                if current_file == file_path:
                    current_lines.append((int(line_number), content, False))  # False表示上下文行

        # 处理最后一组
        if current_file and current_lines:
            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))

        return snippets

    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """从行数据中提取代码片段"""
        snippets = []

        # 按匹配行分组
        i = 0
        while i < len(lines):
            line_number, content, is_match = lines[i]
            if is_match:
                # 收集上下文
                context_before = []
                context_after = []

                # 向前查找上下文
                j = i - 1
                while j >= 0 and not lines[j][2]:  # 上下文行
                    context_before.insert(0, lines[j][1])
                    j -= 1

                # 向后查找上下文
                j = i + 1
                while j < len(lines) and not lines[j][2]:  # 上下文行
                    context_after.append(lines[j][1])
                    j += 1

                snippet = CodeSnippet(
                    file_path=file_path,
                    line_number=line_number,
                    content=content,
                    context_before="\n".join(context_before),
                    context_after="\n".join(context_after)
                )
                snippets.append(snippet)

            i += 1

        return snippets


class SearchManager:
    """搜索管理器"""

    def __init__(self, repo_path: str, search_type: str = "grep"):
        self.repo_path = repo_path
        self.search_type = search_type

        if search_type == "grep":
            self.engine = GrepSearchEngine(repo_path)
        else:
            raise ValueError(f"不支持的搜索类型: {search_type}")

    def search(self, query: str) -> List[CodeSnippet]:
        return self.engine.search(query)

    def search_multiple(self, queries: List[str]):
        results = {}
        for query in queries:
            results[query] = self.search(query)
        return results


class TestGrepSearchEngine(unittest.TestCase):
    """测试GrepSearchEngine的逻辑"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建临时目录和测试文件
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试文件
        self.test_file1 = os.path.join(self.test_dir, "test1.py")
        with open(self.test_file1, 'w', encoding='utf-8') as f:
            f.write("""def hello_world():
    print("Hello, World!")
    return "success"

class TestClass:
    def __init__(self):
        self.name = "test"
    
    def get_name(self):
        return self.name
""")
        
        self.test_file2 = os.path.join(self.test_dir, "test2.js")
        with open(self.test_file2, 'w', encoding='utf-8') as f:
            f.write("""function helloWorld() {
    console.log("Hello, World!");
    return "success";
}

class TestClass {
    constructor() {
        this.name = "test";
    }
    
    getName() {
        return this.name;
    }
}
""")
        
        # 创建应该被过滤的文件
        self.test_file3 = os.path.join(self.test_dir, "test.pyc")
        with open(self.test_file3, 'w', encoding='utf-8') as f:
            f.write("compiled python code")
            
        self.engine = GrepSearchEngine(self.test_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def test_file_filter_valid_extensions(self):
        """测试文件扩展名过滤 - 有效扩展名"""
        self.assertTrue(self.engine._is_valid_file("test.py"))
        self.assertTrue(self.engine._is_valid_file("test.js"))
        self.assertTrue(self.engine._is_valid_file("test.java"))
        
    def test_file_filter_invalid_extensions(self):
        """测试文件扩展名过滤 - 无效扩展名"""
        self.assertFalse(self.engine._is_valid_file("test.pyc"))
        self.assertFalse(self.engine._is_valid_file("test.pyo"))
        self.assertFalse(self.engine._is_valid_file("__pycache__/test.py"))
    
    def test_search_basic_functionality(self):
        """测试基本搜索功能"""
        results = self.engine.search("hello")
        
        # 应该找到结果
        self.assertGreater(len(results), 0)
        
        # 检查结果类型
        for result in results:
            self.assertIsInstance(result, CodeSnippet)
            self.assertIsInstance(result.file_path, str)
            self.assertIsInstance(result.line_number, int)
            self.assertIsInstance(result.content, str)
    
    def test_search_case_insensitive(self):
        """测试大小写不敏感搜索"""
        results_lower = self.engine.search("hello")
        results_upper = self.engine.search("HELLO")
        results_mixed = self.engine.search("Hello")
        
        # 所有搜索应该返回相同数量的结果
        self.assertEqual(len(results_lower), len(results_upper))
        self.assertEqual(len(results_lower), len(results_mixed))
    
    def test_search_with_context(self):
        """测试上下文信息"""
        results = self.engine.search("print")
        
        # 应该找到包含print的结果
        self.assertGreater(len(results), 0)
        
        # 检查是否有上下文信息
        for result in results:
            if "print" in result.content:
                # 应该有上下文（可能为空，但应该是字符串）
                self.assertIsInstance(result.context_before, str)
                self.assertIsInstance(result.context_after, str)
    
    def test_search_no_results(self):
        """测试没有结果的搜索"""
        results = self.engine.search("nonexistent_string_12345")
        self.assertEqual(len(results), 0)
    
    @patch('subprocess.run')
    def test_grep_command_construction(self, mock_run):
        """测试grep命令构造"""
        # 模拟subprocess.run返回
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = ""
        mock_run.return_value = mock_result
        
        self.engine.search("test_query")
        
        # 检查是否调用了subprocess.run
        self.assertTrue(mock_run.called)
        
        # 检查命令参数
        call_args = mock_run.call_args[0][0]
        self.assertIn("grep", call_args)
        self.assertIn("-r", call_args)  # 递归搜索
        self.assertIn("-n", call_args)  # 显示行号
        self.assertIn("-i", call_args)  # 忽略大小写
        self.assertIn("-C", call_args)  # 上下文
        self.assertIn("3", call_args)   # 上下文行数
        self.assertIn("test_query", call_args)  # 查询字符串
    
    def test_parse_grep_output_basic(self):
        """测试grep输出解析 - 基本功能"""
        # 模拟grep输出
        grep_output = """test1.py:2:    print("Hello, World!")
test1.py-1-def hello_world():
test1.py-3-    return "success"
test1.py-4-

test2.js:2:    console.log("Hello, World!");
test2.js-1-function helloWorld() {
test2.js-3-    return "success";
"""
        
        results = self.engine._parse_grep_output(grep_output)
        
        # 应该解析出2个结果
        self.assertEqual(len(results), 2)
        
        # 检查第一个结果
        result1 = results[0]
        self.assertTrue(result1.file_path.endswith("test1.py"))
        self.assertEqual(result1.line_number, 2)
        self.assertIn("print", result1.content)
        self.assertIn("def hello_world", result1.context_before)
        self.assertIn("return", result1.context_after)
    
    def test_parse_grep_output_multiple_matches_same_file(self):
        """测试同一文件多个匹配的解析"""
        grep_output = """test1.py:1:def hello_world():
test1.py:2:    print("Hello, World!")
test1.py:8:    def get_name(self):
"""
        
        results = self.engine._parse_grep_output(grep_output)
        
        # 应该有3个独立的结果
        self.assertEqual(len(results), 3)
        
        # 所有结果都应该来自同一个文件
        for result in results:
            self.assertTrue(result.file_path.endswith("test1.py"))


class TestSearchManager(unittest.TestCase):
    """测试SearchManager的逻辑"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def test_search_manager_initialization(self):
        """测试SearchManager初始化"""
        # 测试grep搜索引擎
        manager = SearchManager(self.test_dir, "grep")
        self.assertIsInstance(manager.engine, GrepSearchEngine)
        
        # 测试embedding搜索引擎
        manager = SearchManager(self.test_dir, "embedding")
        from deep_search.search import EmbeddingSearchEngine
        self.assertIsInstance(manager.engine, EmbeddingSearchEngine)
        
        # 测试无效搜索类型
        with self.assertRaises(ValueError):
            SearchManager(self.test_dir, "invalid_type")
    
    def test_search_multiple_queries(self):
        """测试多查询搜索"""
        manager = SearchManager(self.test_dir, "grep")
        
        queries = ["test1", "test2", "nonexistent"]
        results = manager.search_multiple(queries)
        
        # 应该返回字典，键为查询字符串
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), 3)
        
        for query in queries:
            self.assertIn(query, results)
            self.assertIsInstance(results[query], list)


if __name__ == '__main__':
    unittest.main()
