"""
测试优化后的搜索功能
"""

import os
import sys
import tempfile
import unittest
import logging
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from deep_search.search_optimized import GrepSearchEngineOptimized, SearchManagerOptimized, CodeSnippet


class TestOptimizedGrepSearchEngine(unittest.TestCase):
    """测试优化后的GrepSearchEngine"""
    
    def setUp(self):
        """设置测试环境"""
        # 设置日志
        logging.basicConfig(level=logging.DEBUG)
        
        # 创建临时目录和测试文件
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试文件
        self.test_file1 = os.path.join(self.test_dir, "test1.py")
        with open(self.test_file1, 'w', encoding='utf-8') as f:
            f.write("""def hello_world():
    print("Hello, World!")
    return "success"

class TestClass:
    def __init__(self):
        self.name = "test"
    
    def get_name(self):
        return self.name
""")
        
        self.test_file2 = os.path.join(self.test_dir, "test2.js")
        with open(self.test_file2, 'w', encoding='utf-8') as f:
            f.write("""function helloWorld() {
    console.log("Hello, World!");
    return "success";
}

class TestClass {
    constructor() {
        this.name = "test";
    }
    
    getName() {
        return this.name;
    }
}
""")
        
        # 创建应该被过滤的文件
        self.test_file3 = os.path.join(self.test_dir, "test.pyc")
        with open(self.test_file3, 'w', encoding='utf-8') as f:
            f.write("compiled python code")
            
        self.engine = GrepSearchEngineOptimized(self.test_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def test_file_filter_valid_extensions(self):
        """测试文件扩展名过滤 - 有效扩展名"""
        self.assertTrue(self.engine._is_valid_file("test.py"))
        self.assertTrue(self.engine._is_valid_file("test.js"))
        self.assertTrue(self.engine._is_valid_file("test.java"))
        
    def test_file_filter_invalid_extensions(self):
        """测试文件扩展名过滤 - 无效扩展名"""
        self.assertFalse(self.engine._is_valid_file("test.pyc"))
        self.assertFalse(self.engine._is_valid_file("test.pyo"))
        self.assertFalse(self.engine._is_valid_file("__pycache__/test.py"))
    
    def test_file_filter_nonexistent_file(self):
        """测试不存在文件的处理"""
        # 不存在的文件应该通过扩展名检查（因为可能是相对路径）
        self.assertTrue(self.engine._is_valid_file("nonexistent.py"))
        self.assertFalse(self.engine._is_valid_file("nonexistent.pyc"))
    
    def test_search_basic_functionality(self):
        """测试基本搜索功能"""
        results = self.engine.search("hello")
        
        # 应该找到结果
        self.assertGreater(len(results), 0)
        
        # 检查结果类型
        for result in results:
            self.assertIsInstance(result, CodeSnippet)
            self.assertIsInstance(result.file_path, str)
            self.assertIsInstance(result.line_number, int)
            self.assertIsInstance(result.content, str)
    
    def test_search_case_insensitive(self):
        """测试大小写不敏感搜索"""
        results_lower = self.engine.search("hello")
        results_upper = self.engine.search("HELLO")
        results_mixed = self.engine.search("Hello")
        
        # 所有搜索应该返回相同数量的结果
        self.assertEqual(len(results_lower), len(results_upper))
        self.assertEqual(len(results_lower), len(results_mixed))
    
    def test_search_with_context(self):
        """测试上下文信息"""
        results = self.engine.search("print")
        
        # 应该找到包含print的结果
        self.assertGreater(len(results), 0)
        
        # 检查是否有上下文信息
        for result in results:
            if "print" in result.content:
                # 应该有上下文（可能为空，但应该是字符串）
                self.assertIsInstance(result.context_before, str)
                self.assertIsInstance(result.context_after, str)
    
    def test_search_no_results(self):
        """测试没有结果的搜索"""
        results = self.engine.search("nonexistent_string_12345")
        self.assertEqual(len(results), 0)
    
    def test_parse_grep_output_improved(self):
        """测试改进的grep输出解析"""
        # 模拟grep输出
        grep_output = f"""{self.test_file1}:2:    print("Hello, World!")
{self.test_file1}-1-def hello_world():
{self.test_file1}-3-    return "success"
{self.test_file1}-4-

{self.test_file2}:2:    console.log("Hello, World!");
{self.test_file2}-1-function helloWorld() {{
{self.test_file2}-3-    return "success";
"""
        
        results = self.engine._parse_grep_output(grep_output)
        
        # 应该解析出2个结果
        self.assertEqual(len(results), 2)
        
        # 检查第一个结果
        result1 = results[0]
        self.assertEqual(result1.file_path, self.test_file1)
        self.assertEqual(result1.line_number, 2)
        self.assertIn("print", result1.content)
        self.assertIn("def hello_world", result1.context_before)
        self.assertIn("return", result1.context_after)
    
    def test_caching_functionality(self):
        """测试缓存功能"""
        # 第一次调用
        result1 = self.engine._is_valid_file_cached("test.py")
        
        # 第二次调用应该使用缓存
        result2 = self.engine._is_valid_file_cached("test.py")
        
        self.assertEqual(result1, result2)
        self.assertTrue(result1)  # .py文件应该有效
    
    @patch('subprocess.run')
    def test_timeout_handling(self, mock_run):
        """测试超时处理"""
        import subprocess
        
        # 模拟超时
        mock_run.side_effect = subprocess.TimeoutExpired("grep", 30)
        
        results = self.engine.search("test_query")
        
        # 超时时应该返回空结果
        self.assertEqual(len(results), 0)
    
    @patch('subprocess.run')
    def test_error_code_handling(self, mock_run):
        """测试错误码处理"""
        # 模拟grep返回错误码2（真正的错误）
        mock_result = MagicMock()
        mock_result.returncode = 2
        mock_result.stderr = "grep: invalid option"
        mock_run.return_value = mock_result
        
        results = self.engine.search("test_query")
        
        # 错误时应该返回空结果
        self.assertEqual(len(results), 0)


class TestOptimizedSearchManager(unittest.TestCase):
    """测试优化后的SearchManager"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def test_search_manager_initialization(self):
        """测试SearchManager初始化"""
        # 测试grep搜索引擎
        manager = SearchManagerOptimized(self.test_dir, "grep")
        self.assertIsInstance(manager.engine, GrepSearchEngineOptimized)
        
        # 测试无效搜索类型
        with self.assertRaises(ValueError):
            SearchManagerOptimized(self.test_dir, "invalid_type")
    
    def test_search_multiple_queries_serial(self):
        """测试串行多查询搜索"""
        manager = SearchManagerOptimized(self.test_dir, "grep")
        
        queries = ["test1", "test2", "nonexistent"]
        results = manager.search_multiple(queries, parallel=False)
        
        # 应该返回字典，键为查询字符串
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), 3)
        
        for query in queries:
            self.assertIn(query, results)
            self.assertIsInstance(results[query], list)
    
    def test_search_multiple_queries_parallel(self):
        """测试并行多查询搜索"""
        manager = SearchManagerOptimized(self.test_dir, "grep")
        
        queries = ["test1", "test2", "nonexistent"]
        results = manager.search_multiple(queries, parallel=True)
        
        # 应该返回字典，键为查询字符串
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), 3)
        
        for query in queries:
            self.assertIn(query, results)
            self.assertIsInstance(results[query], list)


class TestCodeSnippet(unittest.TestCase):
    """测试CodeSnippet数据结构"""
    
    def test_get_full_content(self):
        """测试获取完整内容"""
        snippet = CodeSnippet(
            file_path="test.py",
            line_number=10,
            content="main content",
            context_before="before line 1\nbefore line 2",
            context_after="after line 1\nafter line 2"
        )
        
        full_content = snippet.get_full_content()
        expected = "before line 1\nbefore line 2\nmain content\nafter line 1\nafter line 2"
        self.assertEqual(full_content, expected)
    
    def test_get_full_content_no_context(self):
        """测试没有上下文时的完整内容"""
        snippet = CodeSnippet(
            file_path="test.py",
            line_number=10,
            content="main content"
        )
        
        full_content = snippet.get_full_content()
        self.assertEqual(full_content, "main content")


if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    unittest.main(verbosity=2)
