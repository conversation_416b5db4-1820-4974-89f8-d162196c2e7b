# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing_extensions import Literal, Required, TypedDict

__all__ = ["ChatCompletionMessageCustomToolCallParam", "Custom"]


class Custom(TypedDict, total=False):
    input: Required[str]
    """The input for the custom tool call generated by the model."""

    name: Required[str]
    """The name of the custom tool to call."""


class ChatCompletionMessageCustomToolCallParam(TypedDict, total=False):
    id: Required[str]
    """The ID of the tool call."""

    custom: Required[Custom]
    """The custom tool that the model called."""

    type: Required[Literal["custom"]]
    """The type of the tool. Always `custom`."""
