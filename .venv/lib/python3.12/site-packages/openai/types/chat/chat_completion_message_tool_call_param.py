# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing_extensions import TypeAlias

from .chat_completion_message_function_tool_call_param import (
    Function as Function,
    ChatCompletionMessageFunctionToolCallParam,
)

__all__ = ["ChatCompletionMessageToolCallParam", "Function"]

ChatCompletionMessageToolCallParam: TypeAlias = ChatCompletionMessageFunctionToolCallParam
