# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Optional
from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["ResponseCustomToolCall"]


class ResponseCustomToolCall(BaseModel):
    call_id: str
    """An identifier used to map this custom tool call to a tool call output."""

    input: str
    """The input for the custom tool call generated by the model."""

    name: str
    """The name of the custom tool being called."""

    type: Literal["custom_tool_call"]
    """The type of the custom tool call. Always `custom_tool_call`."""

    id: Optional[str] = None
    """The unique ID of the custom tool call in the OpenAI platform."""
