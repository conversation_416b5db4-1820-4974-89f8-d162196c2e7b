"""
搜索功能性能对比测试
比较原始版本和优化版本的性能差异
"""

import os
import sys
import time
import tempfile
import logging
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_repository(base_dir: str, num_files: int = 50) -> str:
    """
    创建测试仓库
    
    Args:
        base_dir: 基础目录
        num_files: 文件数量
        
    Returns:
        str: 测试仓库路径
    """
    repo_dir = os.path.join(base_dir, "test_repo")
    os.makedirs(repo_dir, exist_ok=True)
    
    # 创建Python文件
    for i in range(num_files):
        file_path = os.path.join(repo_dir, f"module_{i}.py")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"""
# Module {i}
import os
import sys
from typing import List, Dict

class TestClass{i}:
    def __init__(self):
        self.name = "test_{i}"
        self.value = {i}
    
    def get_name(self):
        return self.name
    
    def process_data(self, data: List[str]) -> Dict[str, int]:
        result = {{}}
        for item in data:
            result[item] = len(item) + {i}
        return result
    
    def hello_world(self):
        print(f"Hello from module {i}!")
        return "success"

def main():
    instance = TestClass{i}()
    print(instance.get_name())
    data = ["test", "hello", "world"]
    result = instance.process_data(data)
    print(result)
    instance.hello_world()

if __name__ == "__main__":
    main()
""")
    
    # 创建JavaScript文件
    for i in range(num_files // 2):
        file_path = os.path.join(repo_dir, f"script_{i}.js")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"""
// Script {i}
class TestClass{i} {{
    constructor() {{
        this.name = "test_{i}";
        this.value = {i};
    }}
    
    getName() {{
        return this.name;
    }}
    
    processData(data) {{
        const result = {{}};
        data.forEach(item => {{
            result[item] = item.length + {i};
        }});
        return result;
    }}
    
    helloWorld() {{
        console.log(`Hello from script {i}!`);
        return "success";
    }}
}}

function main() {{
    const instance = new TestClass{i}();
    console.log(instance.getName());
    const data = ["test", "hello", "world"];
    const result = instance.processData(data);
    console.log(result);
    instance.helloWorld();
}}

main();
""")
    
    # 创建一些应该被过滤的文件
    for i in range(5):
        file_path = os.path.join(repo_dir, f"compiled_{i}.pyc")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("compiled code")
    
    logger.info(f"创建了测试仓库: {repo_dir}，包含 {num_files} 个Python文件和 {num_files//2} 个JavaScript文件")
    return repo_dir


def benchmark_search_engine(engine, queries: List[str], iterations: int = 3) -> dict:
    """
    基准测试搜索引擎
    
    Args:
        engine: 搜索引擎实例
        queries: 查询列表
        iterations: 迭代次数
        
    Returns:
        dict: 性能统计
    """
    results = {
        'total_time': 0,
        'avg_time_per_query': 0,
        'total_results': 0,
        'queries_tested': len(queries),
        'iterations': iterations
    }
    
    total_time = 0
    total_results = 0
    
    for iteration in range(iterations):
        logger.info(f"开始第 {iteration + 1} 次迭代")
        
        for query in queries:
            start_time = time.time()
            search_results = engine.search(query)
            end_time = time.time()
            
            query_time = end_time - start_time
            total_time += query_time
            total_results += len(search_results)
            
            logger.debug(f"查询 '{query}': {len(search_results)} 个结果, 耗时 {query_time:.3f}s")
    
    results['total_time'] = total_time
    results['avg_time_per_query'] = total_time / (len(queries) * iterations)
    results['total_results'] = total_results
    
    return results


def main():
    """主函数"""
    logger.info("开始搜索功能性能对比测试")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试仓库
        repo_path = create_test_repository(temp_dir, num_files=100)
        
        # 测试查询
        test_queries = [
            "hello",
            "class",
            "def",
            "import",
            "return",
            "print",
            "console.log",
            "function",
            "TestClass",
            "main"
        ]
        
        logger.info(f"测试查询: {test_queries}")
        
        try:
            # 测试优化版本
            logger.info("=" * 50)
            logger.info("测试优化版本搜索引擎")
            logger.info("=" * 50)
            
            from deep_search.search_optimized import GrepSearchEngineOptimized
            optimized_engine = GrepSearchEngineOptimized(repo_path)
            optimized_results = benchmark_search_engine(optimized_engine, test_queries)
            
            logger.info("优化版本测试完成")
            logger.info(f"总耗时: {optimized_results['total_time']:.3f}s")
            logger.info(f"平均每查询耗时: {optimized_results['avg_time_per_query']:.3f}s")
            logger.info(f"总结果数: {optimized_results['total_results']}")
            
        except Exception as e:
            logger.error(f"优化版本测试失败: {e}")
            optimized_results = None
        
        try:
            # 测试原始版本
            logger.info("=" * 50)
            logger.info("测试原始版本搜索引擎")
            logger.info("=" * 50)
            
            from deep_search.search import GrepSearchEngine
            original_engine = GrepSearchEngine(repo_path)
            original_results = benchmark_search_engine(original_engine, test_queries)
            
            logger.info("原始版本测试完成")
            logger.info(f"总耗时: {original_results['total_time']:.3f}s")
            logger.info(f"平均每查询耗时: {original_results['avg_time_per_query']:.3f}s")
            logger.info(f"总结果数: {original_results['total_results']}")
            
        except Exception as e:
            logger.error(f"原始版本测试失败: {e}")
            original_results = None
        
        # 性能对比
        if optimized_results and original_results:
            logger.info("=" * 50)
            logger.info("性能对比结果")
            logger.info("=" * 50)
            
            time_improvement = (original_results['total_time'] - optimized_results['total_time']) / original_results['total_time'] * 100
            
            logger.info(f"原始版本总耗时: {original_results['total_time']:.3f}s")
            logger.info(f"优化版本总耗时: {optimized_results['total_time']:.3f}s")
            logger.info(f"性能提升: {time_improvement:.1f}%")
            
            logger.info(f"原始版本平均耗时: {original_results['avg_time_per_query']:.3f}s")
            logger.info(f"优化版本平均耗时: {optimized_results['avg_time_per_query']:.3f}s")
            
            logger.info(f"原始版本总结果: {original_results['total_results']}")
            logger.info(f"优化版本总结果: {optimized_results['total_results']}")
            
            if optimized_results['total_results'] != original_results['total_results']:
                logger.warning("⚠️  结果数量不一致，可能存在逻辑差异")
            else:
                logger.info("✅ 结果数量一致")
        
        # 测试多查询并行功能
        logger.info("=" * 50)
        logger.info("测试并行搜索功能")
        logger.info("=" * 50)
        
        try:
            from deep_search.search_optimized import SearchManagerOptimized
            manager = SearchManagerOptimized(repo_path)
            
            # 串行测试
            start_time = time.time()
            serial_results = manager.search_multiple(test_queries, parallel=False)
            serial_time = time.time() - start_time
            
            # 并行测试
            start_time = time.time()
            parallel_results = manager.search_multiple(test_queries, parallel=True)
            parallel_time = time.time() - start_time
            
            logger.info(f"串行搜索耗时: {serial_time:.3f}s")
            logger.info(f"并行搜索耗时: {parallel_time:.3f}s")
            
            if parallel_time < serial_time:
                speedup = (serial_time - parallel_time) / serial_time * 100
                logger.info(f"并行搜索提速: {speedup:.1f}%")
            else:
                logger.info("并行搜索未显示明显提速（可能由于查询数量较少）")
                
        except Exception as e:
            logger.error(f"并行搜索测试失败: {e}")
    
    logger.info("性能对比测试完成")


if __name__ == "__main__":
    main()
