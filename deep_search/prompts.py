"""
提示词配置模块
存储所有用于LLM交互的提示词模板
"""

# Query拆分提示词
QUERY_SPLIT_PROMPT = """# Role and Goal
You are a professional code analysis assistant. Your core task is to take the user's original question about a code repository (`original_query`), and break it down into a series (at most three) of more specific, focused sub-questions. These sub-questions are designed to **act as effective code retrieval queries**, guiding the system to find relevant code snippets to answer the user's original intent more precisely.

# Input
- Original Query (`original_query`): user input information
- Search Type:
    - grep: use command to search code, the sub-questions should be specific keywords and searchable
    - embedding: use vector database to search code, the sub-questions should be general and cover all possible answers

# Task Instructions and Rules
1   **Generate Sub-questions (1-3):**
    *   **Highly Relevant**: Each sub-question must closely revolve around the intent of the `original_query`, and its answer should theoretically be findable within the codebase.
    *   **independence**: The sub-question is broken down into independent atomic subtasks to ensure that the original query intent is covered while maintaining independence between tasks.
    *   **Increase Specificity and Searchability**: Sub-questions should be more specific than the original query and **optimized for direct code retrieval**. 
2.  **Handle Special Cases**:
    *   **Sufficiently Specific and Searchable Original Query**: If the `original_query` is already very specific, **suitable as a code retrieval query**, and difficult or unnecessary to break down further, you can generate just one sub-question (which might be the original query itself, or slightly adjusted to better fit a code query).
3.  **Output Format**: The result must be a JSON formatted list of strings.
4.  **Response Language**: chinese

# Output Example (For understanding the task)
Original Query: "解释这个存储级别的仓库的主要功能"
Search Type: grep
Output:
["Repository", "Storage", "Store", "Save", "Load", "Fetch", "Persist", "Read", "Write", "DataSource"]

Original Query: "如何处理用户身份验证，代码在哪里"
Search Type: Embedding
Output:
[
  "查找处理用户登录（login/signin）或注册（register/signup）请求的API端点（Endpoint）或路由（Route）处理函数。",
  "搜索用于验证用户凭据（如密码、令牌）的服务层（Service）或业务逻辑代码，这部分代码通常会与用户数据库进行交互。",
  "定位用于保护路由或API的中间件（Middleware）、守卫（Guard）或拦截器（Interceptor），它们负责检查请求的身份验证状态。"
]

# Start Execution
Please generate the list of sub-questions for the following input according to the instructions and rules above:
Original Query: {original_query}
Search Type: {search_type}
Provide your answer as a JSON list of strings:"""

# 子查询过滤提示词
SUBQUERY_FILTER_PROMPT = """# Role
You are a professional code relevance and answerability assessment expert.

# Goal
Your single task is to determine if a `Code Snippet` is directly helpful for answering a `User Query`. Your final output MUST BE either "YES" or "NO".

# Internal Thought Process (Do not show this in the output)
1.  **Analyze Dimensions**: First, mentally score the snippet from 0-100 on the following dimensions:
    * `Directness` (Weight: 50%): Does the code contain the direct answer or key implementation?
    * `Coverage` (Weight: 25%): How well does it cover the key points of the query?
    * `Confidence` (Weight: 15%): How explicit and verifiable is the information?
    * `Specificity` (Weight: 10%): Is it a specific implementation, or a more general example/config?

2.  **Calculate Score**: Then, mentally compute the total score using the formula:
    `score = round(0.50*Directness + 0.25*Coverage + 0.15*Confidence + 0.10*Specificity, 1)`

3.  **Make Decision**: Finally, apply the rule:
    * If `score >= 60`, the result is "YES".
    * If `score < 60`, the result is "NO".

# Input
User Query:
{query}

Code Snippet:
{code_snippet}

# Output Requirement
Based on your internal thought process, provide your final decision. Your output MUST be strictly "YES" or "NO", without any additional characters, explanation, preamble, or markdown."""

# 生成新查询提示词
GENERATE_NEW_QUERY_PROMPT = """# Role
You are an intelligent assistant tasked with determining whether further, more in-depth searching is required based on the user's current exploration progress (original query, previous sub-queries, retrieved code) to enable a 'drill-down analysis' of the original problem.

# Input Information
1.  **Original Query (question)**: The initial problem the user wants to solve.
    ```
    {question}
    ```
2.  **Previous Sub-queries (mini_questions)**: A list of search queries already executed to address the original query.
    ```
    {mini_questions}
    ```
3.  **Relevant Code Snippets (code_snippet)**: Code examples or information snippets retrieved based on previous queries.
    ```
    {code_snippet}
    ```

# Task & Objective
Based on the input information above, analyze and decide whether further search queries are needed to:
*   **Clarify Ambiguities**: Are there unclear functions, classes, parameters, or concepts within the `code_snippet`?
*   **Explore Details**: Does a specific aspect of the `question` require more concrete examples or explanations?
*   **Find Alternatives/Best Practices**: Is there a need to learn about other implementation methods or best practices related to the current `code_snippet`?
*   **Address Uncovered Sub-problems**: Do the `mini_questions` point to issues not yet adequately addressed by the `code_snippet`?
*   **Satisfy the Depth Requirement of the Original Query**: Especially if the `question` asks for a report, tutorial, or comprehensive explanation, is the current information sufficient (considering diversity, edge cases, comparisons, etc.)?

# Output Requirements
*   **If further search is needed**:
    *   Generate a Python list containing 1 to 2 **specific, targeted** new search query strings. These queries should directly address the information gaps identified in the analysis above.
    *   **Special Consideration**: If the `question` explicitly requests writing a report, providing an explanation, or performing a comparative analysis, **prioritize generating** further queries unless the existing information (`mini_questions` + `code_snippet`) is already highly comprehensive and covers diverse perspectives.
*   **If no further search is needed**:
    *   Return an empty Python list `[]`.

*   **Format**: The response **must** strictly adhere to the format of a valid Python list of strings (List[str]). It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.

# Example Output Format
If queries are needed: `["how to handle python requests timeout error", "python requests set proxy example"]`
If no queries are needed: `[]`

Please process the input information and generate the result according to the instructions above. Please use chinese generate new query"""

# 系统提示词
SYSTEM_PROMPTS = {
    "query_split": "你是一个专业的代码分析助手，擅长将复杂问题拆分为具体的可搜索子问题。",
    "filter": "你是一个专业的代码相关性分析专家，能够准确判断代码片段与查询的相关性。",
    "generate_new_query": "你是一个智能助手，擅长分析当前信息并决定是否需要进一步深入搜索。"
}
