"""
搜索模块
提供不同类型的代码搜索功能
"""

import os
import subprocess
import re
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from .config import FILE_FILTER_CONFIG


@dataclass
class CodeSnippet:
    """代码片段数据结构"""
    file_path: str
    line_number: int
    content: str
    context_before: str = ""
    context_after: str = ""
    
    def get_full_content(self) -> str:
        """获取包含上下文的完整内容"""
        full_content = []
        if self.context_before:
            full_content.append(self.context_before)
        full_content.append(self.content)
        if self.context_after:
            full_content.append(self.context_after)
        return "\n".join(full_content)


class SearchEngine:
    """搜索引擎基类"""
    
    def __init__(self, repo_path: str):
        """
        初始化搜索引擎
        
        Args:
            repo_path: 仓库路径
        """
        self.repo_path = repo_path
        self.file_filter = FILE_FILTER_CONFIG
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        搜索代码片段
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        raise NotImplementedError("子类必须实现search方法")
    
    def _is_valid_file(self, file_path: str) -> bool:
        """
        检查文件是否符合过滤条件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否有效
        """
        # 检查文件扩展名
        if not any(file_path.endswith(ext) for ext in self.file_filter["include_extensions"]):
            return False
        
        # 检查排除的扩展名和目录
        if any(exclude in file_path for exclude in self.file_filter["exclude_extensions"]):
            return False
        
        # 检查文件大小
        try:
            if os.path.getsize(file_path) > self.file_filter["max_file_size"]:
                return False
        except OSError:
            return False
        
        return True


class GrepSearchEngine(SearchEngine):
    """基于grep的搜索引擎"""
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []
        
        try:
            # 构建grep命令
            cmd = [
                "grep",
                "-r",  # 递归搜索
                "-n",  # 显示行号
                "-i",  # 忽略大小写
                "-C", "3",  # 显示上下文3行
                query,
                self.repo_path
            ]
            
            # 添加文件类型过滤
            for ext in self.file_filter["include_extensions"]:
                cmd.extend(["--include", f"*{ext}"])
            
            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            print(f"Grep Command: ", " ".join(cmd))
            
            if result.returncode == 0:
                snippets = self._parse_grep_output(result.stdout)
            
        except Exception as e:
            print(f"Grep搜索失败: {e}")
        
        return snippets
    
    def _parse_grep_output(self, output: str) -> List[CodeSnippet]:
        """
        解析grep输出结果
        
        Args:
            output: grep命令输出
            
        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []
        current_file = None
        current_lines = []
        
        for line in output.split('\n'):
            if not line.strip():
                if current_file and current_lines:
                    snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                    current_lines = []
                current_file = None
                continue
            
            # 解析grep输出格式：文件名:行号:内容 或 文件名-行号-内容（上下文）
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)
            
            if match:
                file_path, line_number, content = match.groups()
                if self._is_valid_file(file_path):
                    current_file = file_path
                    current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                if current_file == file_path:
                    current_lines.append((int(line_number), content, False))  # False表示上下文行
        
        # 处理最后一组
        if current_file and current_lines:
            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
        
        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段
        
        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)
            
        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []
        
        # 按匹配行分组
        i = 0
        while i < len(lines):
            line_number, content, is_match = lines[i]
            if is_match:
                # 收集上下文
                context_before = []
                context_after = []
                
                # 向前查找上下文
                j = i - 1
                while j >= 0 and not lines[j][2]:  # 上下文行
                    context_before.insert(0, lines[j][1])
                    j -= 1
                
                # 向后查找上下文
                j = i + 1
                while j < len(lines) and not lines[j][2]:  # 上下文行
                    context_after.append(lines[j][1])
                    j += 1
                
                snippet = CodeSnippet(
                    file_path=file_path,
                    line_number=line_number,
                    content=content,
                    context_before="\n".join(context_before),
                    context_after="\n".join(context_after)
                )
                snippets.append(snippet)
            
            i += 1
        
        return snippets


class EmbeddingSearchEngine(SearchEngine):
    """基于Embedding的搜索引擎（预留接口）"""
    
    def __init__(self, repo_path: str, embedding_model: str = None):
        """
        初始化Embedding搜索引擎
        
        Args:
            repo_path: 仓库路径
            embedding_model: 嵌入模型名称
        """
        super().__init__(repo_path)
        self.embedding_model = embedding_model
        # TODO: 初始化embedding相关组件
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        使用Embedding搜索代码片段
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        # TODO: 实现embedding搜索逻辑
        print("Embedding搜索功能待实现")
        return []


class SearchManager:
    """搜索管理器"""
    
    def __init__(self, repo_path: str, search_type: str = "grep"):
        """
        初始化搜索管理器
        
        Args:
            repo_path: 仓库路径
            search_type: 搜索类型 ("grep" 或 "embedding")
        """
        self.repo_path = repo_path
        self.search_type = search_type
        
        if search_type == "grep":
            self.engine = GrepSearchEngine(repo_path)
        elif search_type == "embedding":
            self.engine = EmbeddingSearchEngine(repo_path)
        else:
            raise ValueError(f"不支持的搜索类型: {search_type}")
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        执行搜索
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        return self.engine.search(query)
    
    def search_multiple(self, queries: List[str]) -> Dict[str, List[CodeSnippet]]:
        """
        并行执行多个搜索查询
        
        Args:
            queries: 查询字符串列表
            
        Returns:
            Dict[str, List[CodeSnippet]]: 以查询为键的搜索结果字典
        """
        results = {}
        for query in queries:
            results[query] = self.search(query)
        return results
