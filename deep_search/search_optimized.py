"""
优化后的搜索模块
修复了原始搜索模块中的逻辑问题并提供性能优化
"""

import os
import subprocess
import re
import logging
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
from .config import FILE_FILTER_CONFIG


@dataclass
class CodeSnippet:
    """代码片段数据结构"""
    file_path: str
    line_number: int
    content: str
    context_before: str = ""
    context_after: str = ""
    
    def get_full_content(self) -> str:
        """获取包含上下文的完整内容"""
        full_content = []
        if self.context_before:
            full_content.append(self.context_before)
        full_content.append(self.content)
        if self.context_after:
            full_content.append(self.context_after)
        return "\n".join(full_content)


class SearchEngine:
    """搜索引擎基类"""
    
    def __init__(self, repo_path: str):
        """
        初始化搜索引擎
        
        Args:
            repo_path: 仓库路径
        """
        self.repo_path = repo_path
        self.file_filter = FILE_FILTER_CONFIG
        self.logger = logging.getLogger(__name__)
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        搜索代码片段
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        raise NotImplementedError("子类必须实现search方法")
    
    @lru_cache(maxsize=1000)
    def _is_valid_file_cached(self, file_path: str) -> bool:
        """
        缓存版本的文件验证
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否有效
        """
        return self._is_valid_file(file_path)
    
    def _is_valid_file(self, file_path: str) -> bool:
        """
        检查文件是否符合过滤条件（优化版本）
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否有效
        """
        # 检查文件扩展名
        if not any(file_path.endswith(ext) for ext in self.file_filter["include_extensions"]):
            return False
        
        # 检查排除的扩展名和目录
        if any(exclude in file_path for exclude in self.file_filter["exclude_extensions"]):
            return False
        
        # 检查文件大小（只在文件存在时检查）
        try:
            # 构建完整路径
            full_path = file_path if os.path.isabs(file_path) else os.path.join(self.repo_path, file_path)
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                if file_size > self.file_filter["max_file_size"]:
                    self.logger.debug(f"文件 {file_path} 太大 ({file_size} bytes)，跳过")
                    return False
        except OSError as e:
            # 文件访问错误时，记录警告但仍然允许通过
            self.logger.warning(f"无法访问文件 {file_path}: {e}")
        
        return True


class GrepSearchEngineOptimized(SearchEngine):
    """优化后的基于grep的搜索引擎"""
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段（优化版本）
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        self.logger.debug(f"开始搜索: {query}")
        snippets = []
        
        try:
            # 构建grep命令
            cmd = [
                "grep",
                "-r",  # 递归搜索
                "-n",  # 显示行号
                "-i",  # 忽略大小写
                "-C", "3",  # 显示上下文3行
                query,
                self.repo_path
            ]
            
            # 添加文件类型过滤
            for ext in self.file_filter["include_extensions"]:
                cmd.extend(["--include", f"*{ext}"])
            
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=30  # 添加超时保护
            )
            
            if result.returncode == 0:
                snippets = self._parse_grep_output(result.stdout)
            elif result.returncode == 1:
                # grep返回1表示没有找到匹配，这是正常情况
                self.logger.debug("没有找到匹配结果")
            else:
                # 其他错误码表示真正的错误
                self.logger.error(f"Grep命令执行失败 (返回码: {result.returncode}): {result.stderr}")
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"搜索超时: {query}")
        except Exception as e:
            self.logger.error(f"Grep搜索失败: {e}")
        
        self.logger.debug(f"搜索完成，找到 {len(snippets)} 个结果")
        return snippets
    
    def _parse_grep_output(self, output: str) -> List[CodeSnippet]:
        """
        解析grep输出结果（优化版本）
        
        Args:
            output: grep命令输出
            
        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []
        file_groups = {}  # 按文件分组
        current_file = None
        current_lines = []
        
        for line in output.split('\n'):
            if not line.strip():
                if current_file and current_lines:
                    file_groups[current_file] = current_lines
                    current_lines = []
                current_file = None
                continue
            
            # 解析grep输出格式：文件名:行号:内容 或 文件名-行号-内容（上下文）
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)
            
            if match:
                file_path, line_number, content = match.groups()
                current_file = file_path
                current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                # 更宽松的文件路径匹配
                if current_file and (current_file == file_path or 
                                   os.path.basename(current_file) == os.path.basename(file_path)):
                    current_lines.append((int(line_number), content, False))  # False表示上下文行
        
        # 处理最后一组
        if current_file and current_lines:
            file_groups[current_file] = current_lines
        
        # 批量验证文件并提取代码片段
        for file_path, lines in file_groups.items():
            if self._is_valid_file_cached(file_path):
                snippets.extend(self._extract_snippets_from_lines(file_path, lines))
            else:
                self.logger.debug(f"文件 {file_path} 未通过验证，跳过")
        
        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段（优化版本）
        
        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)
            
        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []
        
        # 按匹配行分组
        i = 0
        while i < len(lines):
            line_number, content, is_match = lines[i]
            if is_match:
                # 收集上下文
                context_before = []
                context_after = []
                
                # 向前查找上下文
                j = i - 1
                while j >= 0 and not lines[j][2]:  # 上下文行
                    context_before.insert(0, lines[j][1])
                    j -= 1
                
                # 向后查找上下文
                j = i + 1
                while j < len(lines) and not lines[j][2]:  # 上下文行
                    context_after.append(lines[j][1])
                    j += 1
                
                snippet = CodeSnippet(
                    file_path=file_path,
                    line_number=line_number,
                    content=content,
                    context_before="\n".join(context_before),
                    context_after="\n".join(context_after)
                )
                snippets.append(snippet)
                
                # 跳过已处理的上下文行
                i = j
            else:
                i += 1
        
        return snippets


class SearchManagerOptimized:
    """优化后的搜索管理器"""
    
    def __init__(self, repo_path: str, search_type: str = "grep"):
        """
        初始化搜索管理器
        
        Args:
            repo_path: 仓库路径
            search_type: 搜索类型 ("grep" 或 "embedding")
        """
        self.repo_path = repo_path
        self.search_type = search_type
        self.logger = logging.getLogger(__name__)
        
        if search_type == "grep":
            self.engine = GrepSearchEngineOptimized(repo_path)
        else:
            raise ValueError(f"不支持的搜索类型: {search_type}")
    
    def search(self, query: str) -> List[CodeSnippet]:
        """
        执行搜索
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        return self.engine.search(query)
    
    def search_multiple(self, queries: List[str], parallel: bool = True) -> Dict[str, List[CodeSnippet]]:
        """
        并行执行多个搜索查询（优化版本）
        
        Args:
            queries: 查询字符串列表
            parallel: 是否并行执行
            
        Returns:
            Dict[str, List[CodeSnippet]]: 以查询为键的搜索结果字典
        """
        if not parallel or len(queries) <= 1:
            # 串行执行
            results = {}
            for query in queries:
                results[query] = self.search(query)
            return results
        
        # 并行执行
        results = {}
        with ThreadPoolExecutor(max_workers=min(len(queries), 4)) as executor:
            future_to_query = {executor.submit(self.search, query): query for query in queries}
            
            for future in future_to_query:
                query = future_to_query[future]
                try:
                    results[query] = future.result(timeout=60)
                except Exception as e:
                    self.logger.error(f"查询 '{query}' 执行失败: {e}")
                    results[query] = []
        
        return results
